import type { Chat } from '@/typings/chat'

// 预览调试 SSE 响应数据类型
export interface PreviewStreamData {
  content: string
  reasoningContent: string
  knowledgeSegments?: Chat.ChatContentRecord[]
  isEnd: boolean
}

// 预览调试流式响应管理器
class PreviewStreamManager {
  /**
   * 提取思维链内容（兼容 OpenAI 格式的 <think> 标签）
   */
  private extractThinkingChain(value: string) {
    const newThinkingChains: { content: string }[] = []

    // 处理标签格式
    if (value.includes('<think>')) {
      let hasCompleteThinkBlock = false

      // 首先处理完整的 <think>...</think> 块
      if (value.includes('</think>')) {
        const regex = /<think>([\s\S]*?)<\/think>/g
        let match = regex.exec(value)
        while (match !== null) {
          const thinkingContent = match[1].trim()
          if (thinkingContent) {
            newThinkingChains.push({ content: thinkingContent })
            hasCompleteThinkBlock = true
          }
          match = regex.exec(value)
        }
      }

      // 如果没有完整的think块，处理不完整的情况
      if (!hasCompleteThinkBlock) {
        // 直接提取 <think> 后的内容，即使还没有 </think>
        const thinkStartIndex = value.indexOf('<think>')
        if (thinkStartIndex !== -1) {
          const contentAfterThink = value.substring(thinkStartIndex + 7) // 7 是 '<think>' 的长度
          if (contentAfterThink.trim()) {
            newThinkingChains.push({ content: contentAfterThink.trim() })
          } else {
            // 如果 <think> 后面还没有内容，显示占位符
            newThinkingChains.push({ content: '思考中...' })
          }
        }
      }
    }

    return newThinkingChains
  }

  /**
   * 清理显示文本中的思维链标记
   */
  private cleanThinkingMarkers(text: string): string {
    return text
      .replace(/<think>[\s\S]*?<\/think>/g, '') // 移除完整的 <think>...</think> 块
      .replace(/<think>[\s\S]*$/g, '') // 移除不完整的 <think>... 块
      .trim()
  }

  /**
   * 处理预览调试的 SSE 响应
   */
  async processPreviewStream(
    response: Response,
    onUpdate: (data: PreviewStreamData) => void,
    onError: (error: string) => void,
    controller?: AbortController
  ): Promise<void> {
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    let accumulatedContent = ''
    let accumulatedReasoningContent = ''

    if (!reader) {
      throw new Error('无法获取响应流')
    }

    try {
      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          break
        }

        // 检查是否被取消
        if (controller?.signal.aborted) {
          break
        }

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data:')) {
            const dataContent = line.substring(5).trim()
            if (!dataContent) {
              continue
            }

            try {
              const data = JSON.parse(dataContent)
              console.log('SSE data received:', data) // 调试信息

              if (data.content !== undefined || data.reasoningContent !== undefined) {
                // 处理普通内容
                if (data.content !== undefined) {
                  accumulatedContent += data.content
                  console.log('Updated accumulatedContent:', accumulatedContent.length, 'chars')
                }

                // 处理推理内容
                if (data.reasoningContent !== undefined) {
                  accumulatedReasoningContent += data.reasoningContent
                  console.log(
                    'Updated accumulatedReasoningContent:',
                    accumulatedReasoningContent.length,
                    'chars',
                    'latest chunk:',
                    JSON.stringify(data.reasoningContent),
                  )
                }

                // 如果没有 DeepSeek R1 格式的推理内容，则处理 OpenAI 格式的 <think> 标签
                if (!data.reasoningContent) {
                  // 提取思维链内容（OpenAI格式的兼容处理，支持流式传输中的不完整块）
                  const newThinkingChains = this.extractThinkingChain(accumulatedContent)
                  if (newThinkingChains.length > 0) {
                    // 使用提取到的思维链内容（可能是完整内容或占位符）
                    accumulatedReasoningContent = newThinkingChains
                      .map(chain => chain.content)
                      .join('\n')
                    console.log('Extracted thinking chain:', accumulatedReasoningContent)
                  }
                }

                // 清理显示文本中的思维链标记
                const cleanedContent = this.cleanThinkingMarkers(accumulatedContent)

                // 处理知识库片段数据
                let knowledgeSegments: Chat.ChatContentRecord[] | undefined
                if (data.knowledgeSegments && Array.isArray(data.knowledgeSegments)) {
                  knowledgeSegments = data.knowledgeSegments
                  console.log(
                    'Knowledge segments received:',
                    knowledgeSegments?.length || 0,
                    'segments',
                  )
                }

                // 调用更新回调
                onUpdate({
                  content: cleanedContent,
                  reasoningContent: accumulatedReasoningContent,
                  knowledgeSegments,
                  isEnd: !!data.isEnd,
                })

                // 如果是结束标记，退出循环
                if (data.isEnd) {
                  return
                }
              }
            } catch (error) {
              console.error('解析响应数据失败:', error)
              onError(`解析响应数据失败: ${error}`)
            }
          }
        }
      }
    } catch (error: any) {
      if (error.name === 'AbortError' || error.message === 'canceled') {
        console.log('Stream was cancelled')
      } else {
        console.error('Stream processing error:', error)
        onError(`流处理错误: ${error.message}`)
      }
    } finally {
      reader.releaseLock()
    }
  }
}

// 导出单例实例
export const previewStreamManager = new PreviewStreamManager()
